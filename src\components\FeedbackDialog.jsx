import debounce from "lodash/debounce";
import * as React from "react";
import { Controller, useForm } from "react-hook-form";
import { Alert, View } from "react-native";
import { Button, Dialog, Portal, TextInput } from "react-native-paper"; //import { DialogTextComponent } from "./DialogTextComponent";
import { APP_FEEDBACK_GLOBL as apiConfig } from "../config/keysConfig";
import { standarHttpClient } from "../services/http/standardClient";
import { APP_VERSION, CLIENT_OS, CLIENT_OS_VERSION } from "../services/system";
import { makeFormData } from "../utils";

const CLIENT_INFO = {
    os:     CLIENT_OS,
    osVer:  CLIENT_OS_VERSION,
    appVer: APP_VERSION,
};

/**
 * 弹出一个对话界面，用于填写app的反馈信息.
 * 由于反馈是一个简单的单方面数据传输，因此直接使用了httpClient而没封装react-query。
 * UndismissableDialog.tsx in paper examples
 * @param {object} args
 * @param {string | null | undefined} args.title 标题
 * @param {bool} args.visible 反馈框是否显示，true显示，false不显示
 * @param {function} args.setVisible 设置反馈矿是否显示，其传入参数为true或false
 * @param {string} args.okBtnLabel 确定按钮显示文本
 * @param {string} args.cancelBtnLabel 取消按钮显示文本
 * @returns
 */
export const FeedbackDialog = ({ title, visible, setVisible, okBtnLabel = "提交反馈", cancelBtnLabel = "取消" }) => {
    const httpClient = standarHttpClient;

    const {
        control,
        reset,
        handleSubmit,
    } = useForm({
        defaultValues: {
            content: "",
        },
    });

    // 无论成功与否都会弹出成功提示框，故意延迟弹出以提高真实感。
    const httpOnReplied = debounce(() => {
        Alert.alert("成功", "感谢您的反馈！", [
            { text: "确定", onPress: () => {
                setVisible(false); // 先隐藏后清空的主观感受好些
                reset({ content: "" });
            } },
        ]);
    }, 150);

    // 提交按钮, data由handleSubmit传入
    const onSubmit = (data) => {
        const content = data.content.trim();
        if(content) {
            const formData = makeFormData({ ...CLIENT_INFO, content: content });
            httpClient.post(apiConfig.url, { body: formData })
                .then(httpOnReplied)
                .catch(httpOnReplied);
        } else {
            Alert.alert("", "请输入反馈内容！", [
                { text: "确定", onPress: () => {} },
            ]);
        }
    };
    // 取消按钮
    const onCancel = () => {
        setVisible(false); // 先隐藏后清空的主观感受好些
        reset({ content: "" });
    };

    return (
        <Portal>
            <Dialog onDismiss={() => { setVisible(false); }} visible={visible} dismissable={false}>
                {title && <Dialog.Title>{title}</Dialog.Title>}
                <Dialog.Content>
                    <View flexDirection="column">
                        <Controller
                            control={control}
                            //rules={{ required: true, }}
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    mode="outlined"
                                    multiline={true}
                                    onBlur={onBlur}
                                    onChangeText={onChange}
                                    value={value}
                                    style={styles.input}
                                />
                            )}
                            name="content"
                        />
                    </View>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={onCancel}>{cancelBtnLabel}</Button>
                    <Button onPress={handleSubmit(onSubmit)}>{okBtnLabel}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = {
    input: {
        minHeight: 200,
    }
};
