import React, { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { ScrollView, View } from "react-native";
import { Button, Dialog, TextInput as PaperTextInput, Portal } from "react-native-paper";


/**
 * 用户可更新文本框内容
 * @param {*} props
 * @param {boolean} props.visible
 * @param {function} props.setVisible
 * @param {string} props.defaultText
 * @param {function} props.onRightBtnCallback
 * @param {function} props.onLeftBtnCallback
 * @param {string} props.dialogTitle
 * @param {string} props.rightBtnLabel
 * @param {string} props.leftBtnLabel
 * @returns
 */
export const DialogEditableText = ({ visible, setVisible, defaultText, onLeftBtnCallback, onRightBtnCallback, dialogTitle = "", leftBtnLabel = "更新", rightBtnLabel = "确定", placeholder = "", }) => {
    const {
        control,
        reset,
        handleSubmit,
    } = useForm({
        defaultValues: {
            contents: defaultText,
        },
    });

    useEffect(()=>{
        reset({
            contents: defaultText,
        });
    }, [defaultText]);

    // 右边按钮, data由handleSubmit传入
    const onRightBtnPress = (data) => {
        onRightBtnCallback?.(data.contents);
        //setVisible(false); // 先隐藏后清空的主观感受好些, visible由回调控制
        //reset();           // 恢复默认值
    };
    // 左边按钮, data由handleSubmit传入
    const onLeftBtnPress = (data) => {
        onLeftBtnCallback?.(data.contents);
        //setVisible(false); // 先隐藏后清空的主观感受好些, visible由回调控制
        //reset();           // 恢复默认值
    };

    return (
        <Portal>
            <Dialog onDismiss={() => { setVisible(false); }} visible={visible} dismissable={true}>
                {dialogTitle && <Dialog.Title>{dialogTitle}</Dialog.Title>}
                <Dialog.Content>
                    <View flexDirection="column" style={{ marginTop: 16, height: 240 }}>
                        <ScrollView>
                            {/*<PaperText variant="bodyLarge" style={{margin: 6}}>AI提示词</PaperText>*/}
                            <Controller
                                control={control}
                                name="contents"
                                //rules={{ required: true, }}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <PaperTextInput
                                        mode="outlined"
                                        multiline={true}
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        placeholder={placeholder}
                                        style={{ minHeight: 240, }}
                                    />
                                )}
                            />
                        </ScrollView>
                    </View>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={handleSubmit(onLeftBtnPress)}>{leftBtnLabel}</Button>
                    <Button onPress={handleSubmit(onRightBtnPress)}>{rightBtnLabel}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};
