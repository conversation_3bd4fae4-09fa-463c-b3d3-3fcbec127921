import ky, { HTTPError } from "ky";
import { BASE_URL } from "../../config";
import { STORE_LOGIN_INFO } from "../../config/keysConfig";
import { getMMKV } from "../local-storage";
import { APP_VERSION, CLIENT_OS } from "../system";

// https://github.com/sindresorhus/ky

// Retry config, describled in the book "Creating Apps with React Native, M<PERSON>. He, Apress, 2022"
const RETRY_CODES = [401, 501, 502, 503, 504]; // page 267, 303, 305
const RETRY_METHODS = ["get", "post", "put", "head", "delete", "options", "trace"];
const RETRY_INTERVALS = [0, 2000, 3000, 5000];
//const RETRY_LIMIT = RETRY_INTERVALS.length - 1;
// Timeout in milliseconds for getting a response, including any retries.
const TIMEOUT = 10000;

const getToken = () => getMMKV(STORE_LOGIN_INFO, "token");

/**
 * ky的自定义实例, 用于App中非日志功能的HTTP通信.
 * 用法: httpClient(input, options?), 其中input, options与fetch的参数相同, 但是`credentials`默认是`same-origin`, ky相比fetch添加了更多选项.
 * 简例: const json = await httpClient.post('https://example.com', {json: {foo: true}}).json();
 */
export const standarHttpClient = ky.extend({
    prefixUrl: BASE_URL,
    timeout: TIMEOUT,
    retry: {
        limit: 0, //RETRY_LIMIT, //TypeError: signal.throwIfAborted: https://github.com/sindresorhus/ky/issues/548
        methods: RETRY_METHODS,
        statusCodes: RETRY_CODES,
        delay: attemptCount => RETRY_INTERVALS[attemptCount], // attemptCount starts at 1
    },
    hooks: {
        beforeRequest: [
            (request) => {
                const token = getToken();
                if (!token) {
                    return Promise.reject(new Error("Invalid request."));
                }
                request.headers.set("Authorization", token);
                request.headers.set("APPOS",         CLIENT_OS);
                request.headers.set("AppVersion",    APP_VERSION);
                //console.debug("Ky beforeRequest hooks, set headers:", request.headers);
            },
        ],
        beforeRetry: [
            ({ request, error, retryCount }) => {
                console.log("In Ky beforeRetry hooks");
                if (error instanceof HTTPError && RETRY_CODES.includes(error.response.status) && retryCount > 0) {
                    const token = getToken();
                    if (!token) {
                        return Promise.reject(new Error("Invalid request."));
                    }
                    request.headers.set("Authorization", token);
                    request.headers.set("APPOS",         CLIENT_OS);
                    request.headers.set("AppVersion",    APP_VERSION);
                }
            },
        ],
    },
});


/**
 * ky的自定义实例, 用于App中非日志功能的HTTP通信, 但不获取token, 用于注册等初级消息的发送.
 * 用法: httpClient(input, options?), 其中input, options与fetch的参数相同, 但是`credentials`默认是`same-origin`, ky相比fetch添加了更多选项.
 * 简例: const json = await httpClient.post('https://example.com', {json: {foo: true}}).json();
 */
export const standarHttpClientWithoutToken = ky.extend({
    prefixUrl: BASE_URL,
    timeout: TIMEOUT,
    retry: {
        limit: 0, //RETRY_LIMIT, //TypeError: signal.throwIfAborted: https://github.com/sindresorhus/ky/issues/548
        methods: RETRY_METHODS,
        statusCodes: RETRY_CODES,
        delay: attemptCount => RETRY_INTERVALS[attemptCount], // attemptCount starts at 1
    },
    hooks: {
        beforeRequest: [
            (request) => {
                //const token = getToken();
                //if (!token) {
                //    return Promise.reject(new Error("Invalid request."));
                //}
                //request.headers.set("Authorization", token);
                request.headers.set("APPOS",         CLIENT_OS);
                request.headers.set("AppVersion",    APP_VERSION);
                //console.debug("Ky beforeRequest hooks, set headers:", request.headers);
            },
        ],
        beforeRetry: [
            ({ request, error, retryCount }) => {
                console.log("In Ky beforeRetry hooks");
                if (error instanceof HTTPError && RETRY_CODES.includes(error.response.status) && retryCount > 0) {
                    //const token = getToken();
                    //if (!token) {
                    //    return Promise.reject(new Error("Invalid request."));
                    //}
                    //request.headers.set("Authorization", token);
                    request.headers.set("APPOS",         CLIENT_OS);
                    request.headers.set("AppVersion",    APP_VERSION);
                }
            },
        ],
    },
});

/**
 * 与standarHttpClient区别在于, noTimeoutHttpClient没有超时限制.
 */
export const noTimeoutHttpClient = ky.extend({
    prefixUrl: BASE_URL,
    timeout: false,
    retry: {
        limit: 0, //RETRY_LIMIT, //TypeError: signal.throwIfAborted: https://github.com/sindresorhus/ky/issues/548
        methods: RETRY_METHODS,
        statusCodes: RETRY_CODES,
        delay: attemptCount => RETRY_INTERVALS[attemptCount], // attemptCount starts at 1
    },
    hooks: {
        beforeRequest: [
            (request) => {
                const token = getToken();
                if (!token) {
                    return Promise.reject(new Error("Invalid request."));
                }
                request.headers.set("Authorization", token);
                request.headers.set("APPOS",         CLIENT_OS);
                request.headers.set("AppVersion",    APP_VERSION);
                //console.debug("Ky beforeRequest hooks, set headers:", request.headers);
            },
        ],
        beforeRetry: [
            ({ request, error, retryCount }) => {
                console.log("In Ky beforeRetry hooks");
                if (error instanceof HTTPError && RETRY_CODES.includes(error.response.status) && retryCount > 0) {
                    const token = getToken();
                    if (!token) {
                        return Promise.reject(new Error("Invalid request."));
                    }
                    request.headers.set("Authorization", token);
                    request.headers.set("APPOS",         CLIENT_OS);
                    request.headers.set("AppVersion",    APP_VERSION);
                }
            },
        ],
    },
});


/**  Examples

// 1. call in async function
const json = await ky.post('https://example.com', {json: {foo: true}}).json(); //=> json = `{data: '🦄'}`

// 2. call in JSX
const makeRequest = async () => {
    const response = await httpClient.get("api/v1/username").json(); // get return a response object
    console.log(response);
};
useEffect(() => {
    makeRequest();
}, []);

// 3. complete JSX usage
useEffect(() => {
    const fetchData = async () => {
        try {
            const json = await httpClient("https://example.com").json();
            setData(json);
        } catch (error) {
            if (error instanceof HTTPError) {
                console.error(error.response.status, error.response.statusText);
            } else {
                console.error(error);
            }
            setData(defaultValue);
        }
    };
    fetchData().catch(console.error);
}, []);
*/
